import initSqlJs from 'sql.js';
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs';
import { dirname } from 'path';

/**
 * Count non-null, non-empty fields in a record, excluding metadata fields
 * @param {Object} record - The record to analyze
 * @param {Array<string>} excludeFields - Fields to exclude from counting (metadata fields)
 * @returns {number} - Count of populated fields
 */
function countPopulatedFields(record, excludeFields = ['uuid', 'created_at', 'updated_at']) {
  if (!record || typeof record !== 'object') return 0;

  let count = 0;
  for (const [key, value] of Object.entries(record)) {
    // Skip excluded fields
    if (excludeFields.includes(key)) continue;

    // Count field as populated if it has a meaningful value
    if (value !== null && value !== undefined && value !== '') {
      // For arrays and objects, check if they have content
      if (Array.isArray(value)) {
        if (value.length > 0) count++;
      } else if (typeof value === 'object') {
        if (Object.keys(value).length > 0) count++;
      } else {
        count++;
      }
    }
  }
  return count;
}

/**
 * Compare version numbers for conflict resolution with field completeness fallback
 * @param {string|number|null} incomingVersion - Version number from incoming record
 * @param {string|number|null} existingVersion - Version number from existing database record
 * @param {Object} incomingRecord - The incoming record for field completeness comparison
 * @param {Object} existingRecord - The existing record for field completeness comparison
 * @returns {{shouldUpdate: boolean, reason: string}} - Decision and reason
 */
function shouldUpdateRecord(incomingVersion, existingVersion, incomingRecord = null, existingRecord = null) {
  // Convert to numbers for comparison, treating null/undefined as 0
  const incoming = incomingVersion ? parseInt(incomingVersion, 10) : 0;
  const existing = existingVersion ? parseInt(existingVersion, 10) : 0;

  // Handle invalid version numbers
  if (isNaN(incoming)) return { shouldUpdate: false, reason: 'Invalid incoming version number' };
  if (isNaN(existing)) return { shouldUpdate: true, reason: 'Invalid existing version number' };

  // Version-based comparison
  if (incoming > existing) {
    return { shouldUpdate: true, reason: `Higher version (${incoming} > ${existing})` };
  }
  if (incoming < existing) {
    return { shouldUpdate: false, reason: `Lower version (${incoming} < ${existing})` };
  }

  // Equal versions - check field completeness if records provided
  if (incoming === existing && incomingRecord && existingRecord) {
    const incomingFieldCount = countPopulatedFields(incomingRecord);
    const existingFieldCount = countPopulatedFields(existingRecord);

    if (incomingFieldCount > existingFieldCount) {
      return {
        shouldUpdate: true,
        reason: `Equal version but more complete (${incomingFieldCount} vs ${existingFieldCount} fields)`
      };
    }
    return {
      shouldUpdate: false,
      reason: `Equal version and equal/less complete (${incomingFieldCount} vs ${existingFieldCount} fields)`
    };
  }

  // Equal versions without field comparison - skip by default
  return { shouldUpdate: false, reason: `Equal version (${incoming})` };
}

class DeviceDatabase {
  constructor(dbPath) {
    this.dbPath = dbPath;
    this.db = null;
  }

  async initialize() {
    // Ensure directory exists
    const dir = dirname(this.dbPath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }

    // Initialize SQL.js
    const SQL = await initSqlJs();

    // Try to load existing database or create new one
    if (existsSync(this.dbPath)) {
      const filebuffer = readFileSync(this.dbPath);
      this.db = new SQL.Database(filebuffer);
    } else {
      this.db = new SQL.Database();
    }

    // Create devices table with schema matching mapDeviceSummary output
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS devices (
        uuid TEXT PRIMARY KEY,
        basicUdi TEXT,
        udiDi TEXT,
        risk TEXT,
        name TEXT,
        reference TEXT,
        status TEXT,
        versionNumber TEXT,
        manufacturer_srn TEXT,
        manufacturer_name TEXT,
        manufacturer_status TEXT,
        authorisedRepresentative_srn TEXT,
        authorisedRepresentative_name TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create device_details table with comprehensive schema for mapCombinedDeviceData output
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS device_details (
        uuid TEXT PRIMARY KEY,
        basicUdi TEXT,
        primaryDi TEXT,
        secondaryDi TEXT,
        directMarkingDi TEXT,
        legislation TEXT,
        riskClass TEXT,
        device BOOLEAN,
        specialDeviceType TEXT,

        -- Manufacturer information (JSON)
        manufacturer TEXT,

        -- Authorised Representative information (JSON)
        authorisedRepresentative TEXT,

        -- Device names and descriptions
        deviceName TEXT,
        tradeNames TEXT, -- JSON array
        reference TEXT,
        placedOnTheMarket TEXT,
        marketInfoLinks TEXT, -- JSON array
        additionalDescriptions TEXT, -- JSON array
        additionalInformationUrl TEXT,

        -- Boolean flags
        reprocessed BOOLEAN,
        baseQuantity INTEGER,
        reusable BOOLEAN,
        singleUse BOOLEAN,
        maxNumberOfReuses INTEGER,
        active BOOLEAN,
        administeringMedicine BOOLEAN,
        animalTissues BOOLEAN,
        annexXVIApplicable BOOLEAN,
        companionDiagnostics BOOLEAN,
        endocrineDisruptor BOOLEAN,
        humanTissues BOOLEAN,
        implantable BOOLEAN,
        instrument BOOLEAN,
        kit BOOLEAN,
        latex BOOLEAN,
        measuringFunction BOOLEAN,
        medicinalProduct BOOLEAN,
        microbialSubstances BOOLEAN,
        nearPatientTesting BOOLEAN,
        oemApplicable BOOLEAN,
        professionalTesting BOOLEAN,
        reagent BOOLEAN,
        selfTesting BOOLEAN,
        sterile BOOLEAN,
        sterilization BOOLEAN,
        typeExaminationApplicable BOOLEAN,

        -- Version tracking for conflict resolution
        versionNumber TEXT,

        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes for efficient queries
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_devices_udiDi ON devices(udiDi);
      CREATE INDEX IF NOT EXISTS idx_devices_basicUdi ON devices(basicUdi);
      CREATE INDEX IF NOT EXISTS idx_devices_manufacturer_srn ON devices(manufacturer_srn);
      CREATE INDEX IF NOT EXISTS idx_device_details_primaryDi ON device_details(primaryDi);
      CREATE INDEX IF NOT EXISTS idx_device_details_basicUdi ON device_details(basicUdi);
      CREATE INDEX IF NOT EXISTS idx_device_details_legislation ON device_details(legislation);
      CREATE INDEX IF NOT EXISTS idx_device_details_riskClass ON device_details(riskClass);
    `);

    console.log(`Database initialized at: ${this.dbPath}`);
  }

  saveDevices(devices) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let savedCount = 0;
    let skippedCount = 0;

    // Prepare statements for version checking and insertion
    const checkVersionSql = `SELECT versionNumber, basicUdi, udiDi, risk, name, reference, status,
                             manufacturer_srn, manufacturer_name, manufacturer_status,
                             authorisedRepresentative_srn, authorisedRepresentative_name
                             FROM devices WHERE uuid = ?`;
    const insertSql = `
      INSERT OR REPLACE INTO devices (
        uuid, basicUdi, udiDi, risk, name, reference, status, versionNumber,
        manufacturer_srn, manufacturer_name, manufacturer_status,
        authorisedRepresentative_srn, authorisedRepresentative_name, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `;

    for (const device of devices) {
      try {
        const deviceId = device.uuid;
        if (!deviceId) {
          console.warn('Skipping device without UUID:', device.udiDi || 'unknown');
          skippedCount++;
          continue;
        }

        // Check existing version if record exists
        let shouldUpdate = true;
        let updateReason = 'New record';
        const existingRecord = this.db.exec(checkVersionSql, [deviceId]);

        if (existingRecord.length > 0 && existingRecord[0].values.length > 0) {
          const existingData = existingRecord[0].values[0];
          const existingVersion = existingData[0];

          // Reconstruct existing record for field completeness comparison
          const existingRecordObj = {
            versionNumber: existingData[0],
            basicUdi: existingData[1],
            udiDi: existingData[2],
            risk: existingData[3],
            name: existingData[4],
            reference: existingData[5],
            status: existingData[6],
            manufacturer: {
              srn: existingData[7],
              name: existingData[8],
              status: existingData[9]
            },
            authorisedRepresentative: {
              srn: existingData[10],
              name: existingData[11]
            }
          };

          const updateDecision = shouldUpdateRecord(device.versionNumber, existingVersion, device, existingRecordObj);
          shouldUpdate = updateDecision.shouldUpdate;
          updateReason = updateDecision.reason;

          if (!shouldUpdate) {
            console.log(`Skipping device ${deviceId} - ${updateReason}`);
            skippedCount++;
            continue;
          }
        }

        // Proceed with insert/update
        this.db.run(insertSql, [
          device.uuid || null,
          device.basicUdi || null,
          device.udiDi || null,
          device.risk || null,
          device.name || null,
          device.reference || null,
          device.status || null,
          device.versionNumber || null,
          device.manufacturer?.srn || null,
          device.manufacturer?.name || null,
          device.manufacturer?.status || null,
          device.authorisedRepresentative?.srn || null,
          device.authorisedRepresentative?.name || null
        ]);

        if (updateReason !== 'New record') {
          console.log(`Updated device ${deviceId} - ${updateReason}`);
        }
        savedCount++;
      } catch (error) {
        console.error('Error saving device:', device.uuid || device.udiDi, error.message);
        skippedCount++;
      }
    }

    // Save database to file after batch insert
    try {
      const data = this.db.export();
      writeFileSync(this.dbPath, data);
    } catch (error) {
      console.error('Error saving database to file:', error);
    }

    if (skippedCount > 0) {
      console.log(`Batch complete: ${savedCount} saved, ${skippedCount} skipped due to version conflicts`);
    }

    return savedCount;
  }

  saveDeviceDetails(deviceDetails) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let savedCount = 0;
    let skippedCount = 0;

    // Prepare statements for version checking and insertion
    const checkVersionSql = `SELECT versionNumber, basicUdi, primaryDi, secondaryDi, directMarkingDi,
                             legislation, riskClass, device, specialDeviceType, manufacturer,
                             authorisedRepresentative, deviceName, tradeNames, reference,
                             placedOnTheMarket, marketInfoLinks, additionalDescriptions,
                             additionalInformationUrl, reprocessed, baseQuantity, reusable,
                             singleUse, maxNumberOfReuses, active, administeringMedicine,
                             animalTissues, annexXVIApplicable, companionDiagnostics,
                             endocrineDisruptor, humanTissues, implantable, instrument,
                             kit, latex, measuringFunction, medicinalProduct,
                             microbialSubstances, nearPatientTesting, oemApplicable,
                             professionalTesting, reagent, selfTesting, sterile,
                             sterilization, typeExaminationApplicable
                             FROM device_details WHERE uuid = ?`;
    const insertSql = `
      INSERT OR REPLACE INTO device_details (
        uuid, basicUdi, primaryDi, secondaryDi, directMarkingDi, legislation, riskClass,
        device, specialDeviceType, manufacturer, authorisedRepresentative,
        deviceName, tradeNames, reference, placedOnTheMarket, marketInfoLinks,
        additionalDescriptions, additionalInformationUrl, reprocessed, baseQuantity,
        reusable, singleUse, maxNumberOfReuses, active, administeringMedicine,
        animalTissues, annexXVIApplicable, companionDiagnostics, endocrineDisruptor,
        humanTissues, implantable, instrument, kit, latex, measuringFunction,
        medicinalProduct, microbialSubstances, nearPatientTesting, oemApplicable,
        professionalTesting, reagent, selfTesting, sterile, sterilization,
        typeExaminationApplicable, versionNumber, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `;

    for (const device of deviceDetails) {
      try {
        const deviceId = device.uuid;
        if (!deviceId) {
          console.warn('Skipping device details without UUID:', device.primaryDi || 'unknown');
          skippedCount++;
          continue;
        }

        // Check existing version if record exists
        let shouldUpdate = true;
        let updateReason = 'New record';
        const existingRecord = this.db.exec(checkVersionSql, [deviceId]);

        if (existingRecord.length > 0 && existingRecord[0].values.length > 0) {
          const existingData = existingRecord[0].values[0];
          const existingVersion = existingData[0];

          // Reconstruct existing record for field completeness comparison
          const existingRecordObj = {
            versionNumber: existingData[0],
            basicUdi: existingData[1],
            primaryDi: existingData[2],
            secondaryDi: existingData[3],
            directMarkingDi: existingData[4],
            legislation: existingData[5],
            riskClass: existingData[6],
            device: existingData[7],
            specialDeviceType: existingData[8],
            manufacturer: existingData[9] ? JSON.parse(existingData[9]) : null,
            authorisedRepresentative: existingData[10] ? JSON.parse(existingData[10]) : null,
            deviceName: existingData[11],
            tradeNames: existingData[12] ? JSON.parse(existingData[12]) : null,
            reference: existingData[13],
            placedOnTheMarket: existingData[14],
            marketInfoLinks: existingData[15] ? JSON.parse(existingData[15]) : null,
            additionalDescriptions: existingData[16] ? JSON.parse(existingData[16]) : null,
            additionalInformationUrl: existingData[17],
            reprocessed: existingData[18],
            baseQuantity: existingData[19],
            reusable: existingData[20],
            singleUse: existingData[21],
            maxNumberOfReuses: existingData[22],
            active: existingData[23],
            administeringMedicine: existingData[24],
            animalTissues: existingData[25],
            annexXVIApplicable: existingData[26],
            companionDiagnostics: existingData[27],
            endocrineDisruptor: existingData[28],
            humanTissues: existingData[29],
            implantable: existingData[30],
            instrument: existingData[31],
            kit: existingData[32],
            latex: existingData[33],
            measuringFunction: existingData[34],
            medicinalProduct: existingData[35],
            microbialSubstances: existingData[36],
            nearPatientTesting: existingData[37],
            oemApplicable: existingData[38],
            professionalTesting: existingData[39],
            reagent: existingData[40],
            selfTesting: existingData[41],
            sterile: existingData[42],
            sterilization: existingData[43],
            typeExaminationApplicable: existingData[44]
          };

          const updateDecision = shouldUpdateRecord(device.versionNumber, existingVersion, device, existingRecordObj);
          shouldUpdate = updateDecision.shouldUpdate;
          updateReason = updateDecision.reason;

          if (!shouldUpdate) {
            console.log(`Skipping device details ${deviceId} - ${updateReason}`);
            skippedCount++;
            continue;
          }
        }

        // Proceed with insert/update
        this.db.run(insertSql, [
          device.uuid || null,
          device.basicUdi || null,
          device.primaryDi || null,
          device.secondaryDi || null,
          device.directMarkingDi || null,
          device.legislation || null,
          device.riskClass || null,
          device.device || null,
          device.specialDeviceType || null,
          device.manufacturer ? JSON.stringify(device.manufacturer) : null,
          device.authorisedRepresentative ? JSON.stringify(device.authorisedRepresentative) : null,
          device.deviceName || null,
          device.tradeNames ? JSON.stringify(device.tradeNames) : null,
          device.reference || null,
          device.placedOnTheMarket || null,
          device.marketInfoLinks ? JSON.stringify(device.marketInfoLinks) : null,
          device.additionalDescriptions ? JSON.stringify(device.additionalDescriptions) : null,
          device.additionalInformationUrl || null,
          device.reprocessed || null,
          device.baseQuantity || null,
          device.reusable || null,
          device.singleUse || null,
          device.maxNumberOfReuses || null,
          device.active || null,
          device.administeringMedicine || null,
          device.animalTissues || null,
          device.annexXVIApplicable || null,
          device.companionDiagnostics || null,
          device.endocrineDisruptor || null,
          device.humanTissues || null,
          device.implantable || null,
          device.instrument || null,
          device.kit || null,
          device.latex || null,
          device.measuringFunction || null,
          device.medicinalProduct || null,
          device.microbialSubstances || null,
          device.nearPatientTesting || null,
          device.oemApplicable || null,
          device.professionalTesting || null,
          device.reagent || null,
          device.selfTesting || null,
          device.sterile || null,
          device.sterilization || null,
          device.typeExaminationApplicable || null,
          device.versionNumber || null
        ]);

        if (updateReason !== 'New record') {
          console.log(`Updated device details ${deviceId} - ${updateReason}`);
        }
        savedCount++;
      } catch (error) {
        console.error('Error saving device details:', device.uuid || device.primaryDi, error.message);
        skippedCount++;
      }
    }

    // Save database to file after batch insert
    try {
      const data = this.db.export();
      writeFileSync(this.dbPath, data);
    } catch (error) {
      console.error('Error saving database to file:', error);
    }

    if (skippedCount > 0) {
      console.log(`Device details batch complete: ${savedCount} saved, ${skippedCount} skipped due to version conflicts`);
    }

    return savedCount;
  }

  getDeviceCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM devices');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  getDeviceDetailsCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM device_details');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

export { DeviceDatabase };
