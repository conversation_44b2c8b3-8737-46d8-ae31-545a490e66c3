#!/usr/bin/env node

import { Command } from 'commander';
import { DeviceDatabase } from './lib/database.js';
import { DeviceExtractor } from './lib/deviceExtractor.js';
import { DeviceDetailsExtractor } from './lib/deviceDetailsExtractor.js';
import { join } from 'path';
import { existsSync } from 'fs';

const program = new Command();

program
  .name('eudamed-extract')
  .description('Extract device data from EUDAMED API and save to SQLite database')
  .version('1.0.0');

program
  .option('-o, --output <path>', 'Output SQLite database file path', './data/devices.db')
  .option('-s, --start-page <number>', 'Starting page number (0-based)', '0')
  .option('-e, --end-page <number>', 'Ending page number (0-based, inclusive)')
  .option('-r, --page-range <range>', 'Page range (e.g., "0-10,15,20-25")')
  .option('-c, --concurrency <number>', 'Concurrency limit for parallel requests', '8')
  .option('-p, --page-size <number>', 'Number of devices per page', '300')
  .option('-l, --language <code>', 'Language code for API requests', 'en')
  //not working yet 
  //.option('-d, --extract-details', 'Extract comprehensive device details (3 API calls per device)')
  //.option('-dc, --details-concurrency <number>', 'Concurrency limit for device details requests', '3')
  .option('-t, --test', 'Show what would be extracted without actually doing it')
  .option('-s, --stats', 'Show database statistics after extraction')
  .action(async (options) => {
    try {
      // Parse numeric options
      const startPage = parseInt(options.startPage);
      const endPage = options.endPage ? parseInt(options.endPage) : null;
      const concurrency = parseInt(options.concurrency);
      const pageSize = parseInt(options.pageSize);
      const detailsConcurrency = options.detailsConcurrency ? parseInt(options.detailsConcurrency) : 3;

      // Validate options
      if (isNaN(startPage) || startPage < 0) {
        console.error('Error: Start page must be a non-negative number');
        process.exit(1);
      }

      if (endPage !== null && (isNaN(endPage) || endPage < startPage)) {
        console.error('Error: End page must be a number greater than or equal to start page');
        process.exit(1);
      }

      if (isNaN(concurrency) || concurrency < 1 || concurrency > 20) {
        console.error('Error: Concurrency must be between 1 and 20');
        process.exit(1);
      }

      if (isNaN(pageSize) || pageSize < 1 || pageSize > 1000) {
        console.error('Error: Page size must be between 1 and 1000');
        process.exit(1);
      }

      if (isNaN(detailsConcurrency) || detailsConcurrency < 1 || detailsConcurrency > 10) {
        console.error('Error: Details concurrency must be between 1 and 10');
        process.exit(1);
      }

      if (options.pageRange && (options.endPage || startPage !== 0)) {
        console.error('Error: Cannot use --page-range with --start-page or --end-page');
        process.exit(1);
      }

      console.log('🚀 EUDAMED Device Extractor');
      console.log('============================');
      console.log(`Output database: ${options.output}`);
      console.log(`Page size: ${pageSize}`);
      console.log(`Concurrency: ${concurrency}`);

      if (options.extractDetails) {
        console.log(`🔍 DETAILED EXTRACTION MODE`);
        console.log(`  • Pages processed: Sequential (one at a time)`);
        console.log(`  • Device details: Parallel within each page (concurrency: ${detailsConcurrency})`);
        console.log(`  • API calls per device: 3 (search + basicUdi + details)`);
      } else {
        console.log(`📋 SUMMARY EXTRACTION MODE - 1 API call per page`);
        console.log(`  • Pages processed: Parallel (concurrency: ${concurrency})`);
      }

      if (options.pageRange) {
        console.log(`Page range: ${options.pageRange}`);
      } else if (endPage !== null) {
        console.log(`Pages: ${startPage} to ${endPage}`);
      } else {
        console.log(`Starting from page: ${startPage} (all remaining pages)`);
      }

      if (options.test) {
        console.log('\n⚠️  TEST MODE - No data will be saved');
      }

      console.log('');

      // Initialize extractor based on mode
      const extractor = options.extractDetails
        ? new DeviceDetailsExtractor({
            pageSize,
            concurrencyLimit: concurrency,
            detailsConcurrencyLimit: detailsConcurrency,
            language: options.language
          })
        : new DeviceExtractor({
            pageSize,
            concurrencyLimit: concurrency,
            language: options.language
          });

      // For test mode, just get total pages info
      if (options.test) {
        const { totalPages, totalElements } = await extractor.getTotalPages();

        let pagesToProcess;
        if (options.pageRange) {
          pagesToProcess = extractor.parsePageRange(options.pageRange, totalPages);
        } else if (endPage !== null) {
          const actualEndPage = Math.min(endPage, totalPages - 1);
          pagesToProcess = Array.from({ length: actualEndPage - startPage + 1 }, (_, i) => startPage + i);
        } else {
          pagesToProcess = Array.from({ length: totalPages - startPage }, (_, i) => startPage + i);
        }

        console.log(`\n📊 Test Results:`);
        console.log(`Total pages available: ${totalPages}`);
        console.log(`Total elements available: ${totalElements}`);
        console.log(`Pages that would be processed: ${pagesToProcess.length}`);
        console.log(`Estimated devices to extract: ${pagesToProcess.length * pageSize}`);

        if (options.extractDetails) {
          console.log(`Processing architecture: Sequential pages, parallel device details`);
          console.log(`Estimated page requests: ${pagesToProcess.length} (sequential)`);
          console.log(`Estimated detail requests: ${pagesToProcess.length * pageSize * 2} (2 per device, ${detailsConcurrency} concurrent per page)`);
          console.log(`Total estimated requests: ${pagesToProcess.length + (pagesToProcess.length * pageSize * 2)}`);

          // More accurate time estimation for sequential page processing
          const avgDevicesPerPage = pageSize;
          const timePerPageSeconds = Math.ceil((avgDevicesPerPage * 2) / detailsConcurrency * 2); // 2 seconds per request estimate
          const totalTimeMinutes = Math.ceil((pagesToProcess.length * timePerPageSeconds) / 60);
          console.log(`Estimated time (rough): ${totalTimeMinutes} minutes (sequential page processing)`);
        } else {
          console.log(`Processing architecture: Parallel pages`);
          console.log(`Estimated requests: ${pagesToProcess.length}`);
          console.log(`Estimated time (rough): ${Math.ceil(pagesToProcess.length / concurrency * 2)} seconds`);
        }

        return;
      }

      // Initialize database
      const database = new DeviceDatabase(options.output);
      await database.initialize();

      // Show initial database stats
      if (existsSync(options.output)) {
        const initialCount = database.getDeviceCount();
        console.log(`📊 Initial database contains ${initialCount} devices\n`);
      }

      // Extract devices using appropriate method
      const result = options.extractDetails
        ? await extractor.extractDeviceDetails(database, {
            startPage,
            endPage,
            pageRange: options.pageRange
          })
        : await extractor.extractDevices(database, {
            startPage,
            endPage,
            pageRange: options.pageRange
          });

      // Show results
      console.log('\n✅ Extraction completed!');
      console.log('========================');
      console.log(`Processing time: ${(result.processingTimeMs / 1000).toFixed(2)} seconds`);
      console.log(`Total pages processed: ${result.pagesProcessed}`);
      console.log(`Successful pages: ${result.successfulPages}`);
      console.log(`Failed pages: ${result.failedPages}`);
      console.log(`Total devices saved: ${result.totalSavedRecords}`);

      if (options.extractDetails && result.totalDetailsProcessed) {
        console.log(`Total device details processed: ${result.totalDetailsProcessed}`);
      }

      // Show database stats
      if (options.stats) {
        if (options.extractDetails) {
          const finalDetailsCount = database.getDeviceDetailsCount();
          console.log(`\n📊 Final database contains ${finalDetailsCount} device details`);
        } else {
          const finalCount = database.getDeviceCount();
          console.log(`\n📊 Final database contains ${finalCount} devices`);
        }
      }

      // Show failed pages if any
      if (result.failedPages > 0) {
        console.log('\n⚠️  Failed pages:');
        result.pageResults.forEach(page => {
          console.log(`  Page ${page.pageNumber}: ${page.error}`);
        });
      }

      // Close database
      database.close();

    } catch (error) {
      console.error('\n❌ Error:', error.message);
      process.exit(1);
    }
  });

// Add help examples
program.addHelpText('after', `
Examples:
  $ eudamed-extract                                    # Extract all devices (summary mode)
  $ eudamed-extract --start-page 0 --end-page 10      # Extract first 11 pages
  $ eudamed-extract --page-range "0-5,10,15-20"       # Extract specific pages
  $ eudamed-extract --concurrency 5 --page-size 100   # Custom settings
  $ eudamed-extract --output ./my-devices.db          # Custom output file
  $ eudamed-extract --test                            # Preview what would be extracted
  $ eudamed-extract --stats                           # Show database statistics

  Detailed Extraction (3 API calls per device):
  $ eudamed-extract --extract-details                 # Extract comprehensive device details
  $ eudamed-extract --extract-details --start-page 0 --end-page 2  # Details for first 3 pages
  $ eudamed-extract --extract-details --details-concurrency 2      # Lower concurrency for details
  $ eudamed-extract --extract-details --test          # Preview detailed extraction
`);

program.parse();
