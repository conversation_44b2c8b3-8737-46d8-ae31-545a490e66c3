{"name": "eudamed-device-extractor", "version": "1.0.0", "description": "Standalone CLI tool to extract device data from EUDAMED API", "type": "module", "main": "src/index.js", "bin": {"eudamed-extract": "src/index.js"}, "scripts": {"start": "node src/index.js", "dev": "node src/index.js --test --start-page 0 --end-page 2", "extract": "node src/index.js", "extract-details": "node src/index.js --extract-details", "help": "node src/index.js --help", "test": "node tests/run-tests.js", "test:unit": "node tests/run-tests.js --unit", "test:integration": "node tests/run-tests.js --integration", "test:verbose": "node tests/run-tests.js --verbose", "test:version-conflict": "node tests/unit/version-conflict-resolution.test.js", "demo": "node src/index.js --page-range \"0-1\" --output ./demo.db --stats", "demo-details": "node src/index.js --extract-details --start-page 0 --end-page 1 --output ./demo-details.db --stats"}, "keywords": ["euda<PERSON>", "medical-devices", "cli", "data-extraction"], "author": "", "license": "MIT", "dependencies": {"sql.js": "^1.13.0", "cli-progress": "^3.12.0", "commander": "^11.1.0", "node-fetch": "^3.3.2"}, "engines": {"node": ">=16.0.0"}}