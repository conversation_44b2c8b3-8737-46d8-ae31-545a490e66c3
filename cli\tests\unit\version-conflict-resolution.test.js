#!/usr/bin/env node

/**
 * Unit tests for version-based conflict resolution logic
 *
 * This test suite verifies the shouldUpdateRecord function that determines
 * whether an incoming record should replace an existing record based on
 * version number comparison.
 *
 * Test Coverage:
 * - Higher/lower/equal version comparisons
 * - Null and undefined value handling
 * - String to number conversion
 * - Invalid input handling
 * - Edge cases (zero values, etc.)
 *
 * <AUTHOR> Data Processing Team
 * @since 2024-01-XX
 */

// Import the function under test
// TODO: Replace with actual import when function is extracted to a utility module
// import { shouldUpdateRecord } from '../../src/lib/versionUtils.js';

/**
 * Compare version numbers for conflict resolution
 * This is a copy of the function from database.js for testing purposes.
 * TODO: Extract this to a shared utility module and import it here.
 *
 * @param {string|number|null} incomingVersion - Version number from incoming record
 * @param {string|number|null} existingVersion - Version number from existing database record
 * @returns {boolean} - True if incoming version should replace existing, false if should skip
 */
function shouldUpdateRecord(incomingVersion, existingVersion) {
  // Convert to numbers for comparison, treating null/undefined as 0
  const incoming = incomingVersion ? parseInt(incomingVersion, 10) : 0;
  const existing = existingVersion ? parseInt(existingVersion, 10) : 0;

  // Handle invalid version numbers
  if (isNaN(incoming)) return false;
  if (isNaN(existing)) return true;

  // Only update if incoming version is higher than existing
  return incoming > existing;
}

// Test cases
const testCases = [
  // [incomingVersion, existingVersion, expectedResult, description]
  [2, 1, true, "Higher incoming version should update"],
  [1, 2, false, "Lower incoming version should skip"],
  [1, 1, false, "Equal versions should skip"],
  [1, null, true, "Incoming version vs null existing should update"],
  [null, 1, false, "Null incoming vs existing version should skip"],
  [null, null, false, "Both null should skip"],
  ["2", "1", true, "String versions - higher should update"],
  ["1", "2", false, "String versions - lower should skip"],
  ["invalid", "1", false, "Invalid incoming version should skip"],
  ["1", "invalid", true, "Invalid existing version should update"],
  [0, 1, false, "Zero incoming vs positive existing should skip"],
  [1, 0, true, "Positive incoming vs zero existing should update"],
  [0, 0, false, "Both zero should skip"],
];

/**
 * Test runner for version conflict resolution
 */
function runTests() {
  console.log("🧪 Running Unit Tests: Version Conflict Resolution Logic");
  console.log("=".repeat(60));
  console.log();

  let passed = 0;
  let failed = 0;
  const failures = [];

  testCases.forEach(([incoming, existing, expected, description], index) => {
    const testNumber = index + 1;
    const result = shouldUpdateRecord(incoming, existing);
    const success = result === expected;

    if (success) {
      console.log(`✅ Test ${testNumber.toString().padStart(2, '0')}: ${description}`);
      passed++;
    } else {
      console.log(`❌ Test ${testNumber.toString().padStart(2, '0')}: ${description}`);
      console.log(`   Expected: ${expected}, Got: ${result}`);
      console.log(`   Incoming: ${incoming}, Existing: ${existing}`);
      failed++;
      failures.push({
        testNumber,
        description,
        expected,
        actual: result,
        inputs: { incoming, existing }
      });
    }
  });

  console.log();
  console.log("=".repeat(60));
  console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);

  if (failed === 0) {
    console.log("🎉 All tests passed! Version conflict resolution logic is working correctly.");
    console.log();
    console.log("✅ Test Coverage:");
    console.log("   - Version number comparisons (higher/lower/equal)");
    console.log("   - Null and undefined value handling");
    console.log("   - String to number conversion");
    console.log("   - Invalid input handling");
    console.log("   - Edge cases (zero values)");
    return true;
  } else {
    console.log("❌ Test failures detected:");
    failures.forEach(failure => {
      console.log(`   Test ${failure.testNumber}: ${failure.description}`);
    });
    console.log();
    console.log("Please review the shouldUpdateRecord function implementation.");
    return false;
  }
}

// Execute tests if this file is run directly
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
if (process.argv[1] === __filename) {
  const success = runTests();
  process.exit(success ? 0 : 1);
}

// Export for use in other test files
export { shouldUpdateRecord, runTests };
